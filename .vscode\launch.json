{"version": "0.2.0", "configurations": [{"name": "(gdb) Attach", "type": "cppdbg", "request": "attach", "program": "enter program name, for example ${workspaceFolder}/a.exe", "MIMode": "gdb", "miDebuggerPath": "/path/to/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "(gdb) Launch", "type": "cppdbg", "request": "launch", "program": "enter program name, for example ${workspaceFolder}/a.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/path/to/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/iCloud Drive/iCloudDrive/Tà<PERSON> liệu môn học kì 2 năm 4/<PERSON><PERSON><PERSON><PERSON> đề công nghệ phần mềm/<PERSON><PERSON><PERSON> trình theo thuật toán", "program": "d:/iCloud Drive/iCloudDrive/Tà<PERSON> liệu môn học kì 2 năm 4/<PERSON><PERSON><PERSON>n đề công nghệ phần mềm/<PERSON><PERSON><PERSON> trình theo thuật toán/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}