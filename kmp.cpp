#include <stdio.h>
#include <string.h>

#define MAX 1000

void preKmp(char *x, int m, int kmpNext[]) {
    int i = 0, j = -1;
    kmpNext[0] = -1;

    while (i < m) {
        while (j > -1 && x[i] != x[j]) j = kmpNext[j];
        i++; j++;
        if (x[i] == x[j])
            kmpNext[i] = kmpNext[j];
        else
            kmpNext[i] = j;
    }
}

void KMP(char *x, int m, char *y, int n) {
    int i = 0, j = 0;
    int kmpNext[MAX];

    preKmp(x, m, kmpNext);

    while (j < n) {
        while (i > -1 && x[i] != y[j]) i = kmpNext[i];
        i++; j++;
        if (i >= m) {
            printf("Tìm thấy tại vị trí: %d\n", j - i);
            i = kmpNext[i];
        }
    }
}

int main() {
    char text[MAX], pattern[MAX];

    printf("Nhập văn bản: ");
    fgets(text, MAX, stdin);
    printf("Nhập mẫu: ");
    fgets(pattern, MAX, stdin);

    // Xoá newline
    text[strcspn(text, "\n")] = 0;
    pattern[strcspn(pattern, "\n")] = 0;

    int n = strlen(text);
    int m = strlen(pattern);

    KMP(pattern, m, text, n);
    return 0;
}
